

defaultproperties
{
     Normal3on3Settings=(MaxPlayers=6,MaxSpectators=12,RoundTimeLimit=12,ReinforcementsFreq=4)
     Normal4on4Settings=(MaxPlayers=8,MaxSpectators=12,RoundTimeLimit=20,ReinforcementsFreq=7)
     Normal5on5Settings=(MaxPlayers=10,MaxSpectators=12,RoundTimeLimit=20,ReinforcementsFreq=7)
     Train3on3Settings=(MaxPlayers=6,MaxSpectators=12)
     Train4on4Settings=(MaxPlayers=8,MaxSpectators=12)
     Train5on5Settings=(MaxPlayers=10,MaxSpectators=12)
     NamePlaceHolder="%Name"
     TimePlaceHolder="%Time"
     VotePlaceHolder="%Vote"
     SettingsPlaceHolder="%Settings"
     AccessPlaceHolder="%Access"
     AccessText="���Press '�QQF7���' or type '�QQMutate ASeXtra���' in Console"
     DisabledNextObjectiveText="���%Name ���disabled next Objective"
     MapRestartText="���%Name ���is restarting the Map"
     MapRestartInfoText="���Map Restarts in �QQ%Time"
     PauseText="���%Name ���paused the game"
     UnPauseText="���%Name ���unpaused the game"
     UnPauseInfoText="���Game Resumes in �QQ%Time"
     SwitchPlayersText="���%Name ���switched all players"
     SettingsSetText="���%Name ���Set %Settings"
     VoteStartedText="���%Name ���started an ASeXtra vote. %Access to vote!"
     VoteInProgressText="���There is already a vote in progress"
     VoteYesText="���%Name ���voted yes!"
     VoteNoText="���%Name ���voted no!"
     VotePassedText="���ASeXtra Vote Passed! (%Vote)"
     VoteFailedText="���ASeXtra Vote Failed! (%Vote)"
     VoteTimeoutText="���ASeXtra Settings Vote Timeout"
     IsUserAlreadyText="���%Name ���is already an Allowed User"
     UserAddSuccessText="���%Name ���was successfully added to the Allowed Users list"
     UserAddFailedText="���Could not find Player %Name"
     UserRemovedText="���%Name ���was successfully removed from the Allowed Users list"
     PermissionsGrantedText="���%Name ���granted you Access Permissions to ASeXtra. %Access!"
     PermissionsRevokedText="���Your Access Permissions to ASeXtra were revoked by %Name"
     NoPermissionsText="���You do not have the Permissions to do this!"
     RunningVeryFirstTime=True
     ForceSettingsEnabled=True
     VotingEnabled=True
     ShowDisabledNextObjectiveMessage=True
     ShowMapRestartMessage=True
     ShowPauseMessage=True
     ShowUnPauseMessage=True
     ShowSwitchPlayersMessage=True
     ReplaceUTCompHUD=True
     RestartTime=3
     UnPauseTime=5
     VoteTime=30
     Modes(0)="3on3 Settings"
     Modes(1)="4on4 Settings"
     Modes(2)="5on5 Settings"
     Modes(3)="3on3 Train Settings"
     Modes(4)="4on4 Train Settings"
     Modes(5)="5on5 Train Settings"
     Modes(6)="Soft Map Restart"
     menus(0)="ASeXtra.eXtraMenu"
     menus(1)="ASeXtra.InfoMenu"
     menus(2)="ASeXtra.VoteMenu"
     menus(3)="ASeXtra.CustomMenu"
     menus(4)="ASeXtra.SettingsMenu"
     menus(5)="ASeXtra.UsersMenu"
     NotifySound=Sound'MenuSounds.select3'
     CountDownSound=Sound'2K4MenuSounds.Generic.msfxMouseOver'
     FixedHUDClass="ASeXtra.eXtraHUD"
     bAddToServerPackages=True
     FriendlyName="AS eXtra Mutator"
     Description="Including features to simplify Assault Server Management"
     bAlwaysTick=True
}
