

defaultproperties
{
     Begin Object Class=FloatingImage Name=VoteBackGround
         Image=Texture'2K4Menus.NewControls.Display95'
         DropShadow=None
         ImageStyle=ISTY_Stretched
         ImageRenderStyle=MSTY_Normal
         WinTop=0.350000
         WinLeft=0.300000
         WinWidth=0.400000
         WinHeight=0.300000
         RenderWeight=0.030000
         bBoundToParent=False
         bScaleToParent=False
     End Object
     BG_Vote=FloatingImage'ASeXtra.VoteMenu.VoteBackGround'

     Begin Object Class=GUILabel Name=VoteInfoLabel
         Caption="..."
         TextAlign=TXTA_Center
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.480000
         WinLeft=0.326000
         WinWidth=0.348000
         WinHeight=0.077000
     End Object
     L_VoteInfo=GUILabel'ASeXtra.VoteMenu.VoteInfoLabel'

     Begin Object Class=GUIComboBox Name=VoteCombo
         FontScale=FNS_Small
         WinTop=0.400000
         WinLeft=0.326000
         WinWidth=0.348000
         WinHeight=0.027500
         TabOrder=1
         OnKeyEvent=VoteCombo.InternalOnKeyEvent
     End Object
     CB_Vote=GUIComboBox'ASeXtra.VoteMenu.VoteCombo'

     Begin Object Class=GUIButton Name=StartVoteButton
         Caption="Start New Vote"
         FontScale=FNS_Small
         WinTop=0.430000
         WinLeft=0.325000
         WinWidth=0.350000
         WinHeight=0.050000
         TabOrder=2
         OnClick=VoteMenu.InternalOnClick
         OnKeyEvent=StartVoteButton.InternalOnKeyEvent
     End Object
     B_StartVote=GUIButton'ASeXtra.VoteMenu.StartVoteButton'

     Begin Object Class=GUIButton Name=VoteYesButton
         Caption="Vote Yes!"
         FontScale=FNS_Small
         WinTop=0.557000
         WinLeft=0.325000
         WinWidth=0.175000
         WinHeight=0.050000
         TabOrder=3
         OnClick=VoteMenu.InternalOnClick
         OnKeyEvent=VoteYesButton.InternalOnKeyEvent
     End Object
     B_VoteYes=GUIButton'ASeXtra.VoteMenu.VoteYesButton'

     Begin Object Class=GUIButton Name=votenoButton
         Caption="Vote No!"
         FontScale=FNS_Small
         WinTop=0.557000
         WinLeft=0.500000
         WinWidth=0.175000
         WinHeight=0.050000
         TabOrder=4
         OnClick=VoteMenu.InternalOnClick
         OnKeyEvent=votenoButton.InternalOnKeyEvent
     End Object
     B_VoteNo=GUIButton'ASeXtra.VoteMenu.votenoButton'

     bAllowedAsLast=True
     WinTop=0.000000
     WinWidth=0.000000
     WinHeight=0.000000
     OnTimer=VoteMenu.MyTimer
}
