class CustomMenu extends PopupPageBase
	editinlinenew
	localized
	config(User);

var() automated FloatingImage BG_Custom;
var() automated GUILabel l_MaxPlayers;
var() automated GUILabel L_MaxSpectators;
var() automated GUILabel L_SpawnProtectionTime;
var() automated GUILabel L_RoundTimeLimit;
var() automated GUILabel L_ReinforcementsFreq;
var() automated GUILabel L_PracticeTimeLimit;
var() automated GUIEditBox E_MaxPlayers;
var() automated GUIEditBox E_MaxSpectators;
var() automated GUIEditBox E_SpawnProtectionTime;
var() automated GUIEditBox E_RoundTimeLimit;
var() automated GUIEditBox E_ReinforcementsFreq;
var() automated GUIEditBox E_PracticeTimeLimit;
var() automated GUIButton b_Apply;
var array<string> Data;

function InitComponent(GUIController MyController, GUIComponent MyOwner)
{
	Super.InitComponent(MyController, MyOwner);

	SetTimer(0.100000, true);
}

function bool InternalOnClick(GUIComponent Sender)
{
	local PlayerController PC;
	local eXtraLRI LRI;

	PC = PlayerOwner();
	LRI = class'ASeXtra.eXtraHelper'.static.GetLRI(PC);

	if (Sender == b_Apply)
	{
		LRI.ServerSetCustomGameSettings(byte(E_MaxPlayers.TextStr), byte(E_MaxSpectators.TextStr), byte(E_SpawnProtectionTime.TextStr), byte(E_RoundTimeLimit.TextStr), byte(E_ReinforcementsFreq.TextStr), byte(E_PracticeTimeLimit.TextStr));
		PC.ClientCloseMenu();
		return true;
	}

	return true;
}

function MyTimer(GUIComponent Sender)
{
	if (Data.Length == 6)
	{
		KillTimer();
		E_MaxPlayers.TextStr = Data[0];
		E_MaxSpectators.TextStr = Data[1];
		E_SpawnProtectionTime.TextStr = Data[2];
		E_RoundTimeLimit.TextStr = Data[3];
		E_ReinforcementsFreq.TextStr = Data[4];
		E_PracticeTimeLimit.TextStr = Data[5];

		SetTimer(0.000000, false);
	}
}

event HandleParameters(string Param1, string Param2)
{
	local int SpaceIndex;

	J0x00:
	// End:0x72 [Loop If]
	if (true)
	{
		SpaceIndex = InStr(Param1, " ");
		// End:0x38
		if (SpaceIndex == -1)
		{
			Data[Data.Length] = Param1;
			// [Explicit Break]
			goto J0x72;
		}
		Data[Data.Length] = Left(Param1, SpaceIndex);
		Param1 = Right(Param1, (Len(Param1) - SpaceIndex) - 1);
		// [Loop Continue]
		goto J0x00;
	}
	J0x72:
}

defaultproperties
{
     Begin Object Class=FloatingImage Name=CustomBackGround
         Image=Texture'2K4Menus.NewControls.Display95'
         DropShadow=None
         ImageStyle=ISTY_Stretched
         ImageRenderStyle=MSTY_Normal
         WinTop=0.275000
         WinLeft=0.300000
         WinWidth=0.400000
         WinHeight=0.450000
         RenderWeight=0.020000
         bBoundToParent=False
         bScaleToParent=False
     End Object
     BG_Custom=FloatingImage'ASeXtra.CustomMenu.CustomBackGround'

     Begin Object Class=GUILabel Name=MaxPlayersLabel
         Caption="MaxPlayers: (Instant)"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.225000
         WinLeft=0.350000
         WinWidth=0.200000
         WinHeight=0.250000
     End Object
     l_MaxPlayers=GUILabel'ASeXtra.CustomMenu.MaxPlayersLabel'

     Begin Object Class=GUILabel Name=MaxSpectatorsLabel
         Caption="MaxSpectators: (Instant)"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.275000
         WinLeft=0.350000
         WinWidth=0.200000
         WinHeight=0.250000
     End Object
     L_MaxSpectators=GUILabel'ASeXtra.CustomMenu.MaxSpectatorsLabel'

     Begin Object Class=GUILabel Name=SpawnProtectionTimeLabel
         Caption="SpawnProtectionTime: (Instant)"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.325000
         WinLeft=0.350000
         WinWidth=0.200000
         WinHeight=0.250000
     End Object
     L_SpawnProtectionTime=GUILabel'ASeXtra.CustomMenu.SpawnProtectionTimeLabel'

     Begin Object Class=GUILabel Name=RoundTimeLimitLabel
         Caption="RoundTimeLimit: (Requires Restart)"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.375000
         WinLeft=0.350000
         WinWidth=0.200000
         WinHeight=0.250000
     End Object
     L_RoundTimeLimit=GUILabel'ASeXtra.CustomMenu.RoundTimeLimitLabel'

     Begin Object Class=GUILabel Name=ReinforcementsFreqLabel
         Caption="ReinforcementsFreq: (Instant)"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.425000
         WinLeft=0.350000
         WinWidth=0.200000
         WinHeight=0.250000
     End Object
     L_ReinforcementsFreq=GUILabel'ASeXtra.CustomMenu.ReinforcementsFreqLabel'

     Begin Object Class=GUILabel Name=PracticeTimeLimitLabel
         Caption="PracticeTimeLimit: (Requires Restart)"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.475000
         WinLeft=0.350000
         WinWidth=0.200000
         WinHeight=0.250000
     End Object
     L_PracticeTimeLimit=GUILabel'ASeXtra.CustomMenu.PracticeTimeLimitLabel'

     Begin Object Class=GUIEditBox Name=MaxPlayersEdit
         TextStr="00"
         AllowedCharSet="1234567890"
         MaxWidth=2
         FontScale=FNS_Small
         WinTop=0.337500
         WinLeft=0.574000
         WinWidth=0.075000
         WinHeight=0.022500
         TabOrder=1
         OnActivate=MaxPlayersEdit.InternalActivate
         OnDeActivate=MaxPlayersEdit.InternalDeactivate
         OnKeyType=MaxPlayersEdit.InternalOnKeyType
         OnKeyEvent=MaxPlayersEdit.InternalOnKeyEvent
     End Object
     E_MaxPlayers=GUIEditBox'ASeXtra.CustomMenu.MaxPlayersEdit'

     Begin Object Class=GUIEditBox Name=MaxSpectatorsEdit
         TextStr="00"
         AllowedCharSet="1234567890"
         MaxWidth=2
         FontScale=FNS_Small
         WinTop=0.387500
         WinLeft=0.574000
         WinWidth=0.075000
         WinHeight=0.022500
         TabOrder=2
         OnActivate=MaxSpectatorsEdit.InternalActivate
         OnDeActivate=MaxSpectatorsEdit.InternalDeactivate
         OnKeyType=MaxSpectatorsEdit.InternalOnKeyType
         OnKeyEvent=MaxSpectatorsEdit.InternalOnKeyEvent
     End Object
     E_MaxSpectators=GUIEditBox'ASeXtra.CustomMenu.MaxSpectatorsEdit'

     Begin Object Class=GUIEditBox Name=SpawnProtectionTimeEdit
         TextStr="00"
         AllowedCharSet="1234567890"
         MaxWidth=2
         FontScale=FNS_Small
         WinTop=0.437500
         WinLeft=0.574000
         WinWidth=0.075000
         WinHeight=0.022500
         TabOrder=3
         OnActivate=SpawnProtectionTimeEdit.InternalActivate
         OnDeActivate=SpawnProtectionTimeEdit.InternalDeactivate
         OnKeyType=SpawnProtectionTimeEdit.InternalOnKeyType
         OnKeyEvent=SpawnProtectionTimeEdit.InternalOnKeyEvent
     End Object
     E_SpawnProtectionTime=GUIEditBox'ASeXtra.CustomMenu.SpawnProtectionTimeEdit'

     Begin Object Class=GUIEditBox Name=RoundTimeLimitEdit
         TextStr="00"
         AllowedCharSet="1234567890"
         MaxWidth=2
         FontScale=FNS_Small
         WinTop=0.487500
         WinLeft=0.574000
         WinWidth=0.075000
         WinHeight=0.022500
         TabOrder=4
         OnActivate=RoundTimeLimitEdit.InternalActivate
         OnDeActivate=RoundTimeLimitEdit.InternalDeactivate
         OnKeyType=RoundTimeLimitEdit.InternalOnKeyType
         OnKeyEvent=RoundTimeLimitEdit.InternalOnKeyEvent
     End Object
     E_RoundTimeLimit=GUIEditBox'ASeXtra.CustomMenu.RoundTimeLimitEdit'

     Begin Object Class=GUIEditBox Name=ReinforcementsFreqEdit
         TextStr="00"
         AllowedCharSet="1234567890"
         MaxWidth=2
         FontScale=FNS_Small
         WinTop=0.537500
         WinLeft=0.574000
         WinWidth=0.075000
         WinHeight=0.022500
         TabOrder=5
         OnActivate=ReinforcementsFreqEdit.InternalActivate
         OnDeActivate=ReinforcementsFreqEdit.InternalDeactivate
         OnKeyType=ReinforcementsFreqEdit.InternalOnKeyType
         OnKeyEvent=ReinforcementsFreqEdit.InternalOnKeyEvent
     End Object
     E_ReinforcementsFreq=GUIEditBox'ASeXtra.CustomMenu.ReinforcementsFreqEdit'

     Begin Object Class=GUIEditBox Name=PracticeTimeLimitEdit
         TextStr="00"
         AllowedCharSet="1234567890"
         MaxWidth=2
         FontScale=FNS_Small
         WinTop=0.587500
         WinLeft=0.574000
         WinWidth=0.075000
         WinHeight=0.022500
         TabOrder=6
         OnActivate=PracticeTimeLimitEdit.InternalActivate
         OnDeActivate=PracticeTimeLimitEdit.InternalDeactivate
         OnKeyType=PracticeTimeLimitEdit.InternalOnKeyType
         OnKeyEvent=PracticeTimeLimitEdit.InternalOnKeyEvent
     End Object
     E_PracticeTimeLimit=GUIEditBox'ASeXtra.CustomMenu.PracticeTimeLimitEdit'

     Begin Object Class=GUIButton Name=ApplyButton
         Caption="Set Custom Settings"
         FontScale=FNS_Small
         WinTop=0.625000
         WinLeft=0.350000
         WinWidth=0.300000
         WinHeight=0.050000
         TabOrder=7
         OnClick=CustomMenu.InternalOnClick
         OnKeyEvent=ApplyButton.InternalOnKeyEvent
     End Object
     b_Apply=GUIButton'ASeXtra.CustomMenu.ApplyButton'

     bAllowedAsLast=True
     WinTop=0.000000
     WinWidth=0.000000
     WinHeight=0.000000
     OnTimer=CustomMenu.MyTimer
}
