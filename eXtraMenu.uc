

defaultproperties
{
     Begin Object Class=FloatingImage Name=eXtraBackGround
         Image=Texture'2K4Menus.NewControls.Display95'
         DropShadow=None
         ImageStyle=ISTY_Stretched
         ImageRenderStyle=MSTY_Normal
         WinTop=0.225000
         WinLeft=0.300000
         WinWidth=0.400000
         WinHeight=0.550000
         RenderWeight=0.010000
         bBoundToParent=False
         bScaleToParent=False
     End Object
     BG_eXtra=FloatingImage'ASeXtra.eXtraMenu.eXtraBackGround'

     Begin Object Class=GUILabel Name=MainLabel
         Caption="Set settings for:"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.212500
         WinLeft=0.353000
         WinWidth=0.200000
         WinHeight=0.250000
     End Object
     L_Main=GUILabel'ASeXtra.eXtraMenu.MainLabel'

     Begin Object Class=GUIButton Name=InfoButton
         Caption="AS eXtra Info"
         FontScale=FNS_Small
         WinTop=0.275000
         WinLeft=0.350000
         WinWidth=0.300000
         WinHeight=0.050000
         TabOrder=1
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=InfoButton.InternalOnKeyEvent
     End Object
     b_Info=GUIButton'ASeXtra.eXtraMenu.InfoButton'

     Begin Object Class=GUIButton Name=3on3Button
         Caption="3on3"
         FontScale=FNS_Small
         WinTop=0.350000
         WinLeft=0.350000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=2
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=3on3Button.InternalOnKeyEvent
     End Object
     B_3on3=GUIButton'ASeXtra.eXtraMenu.3on3Button'

     Begin Object Class=GUIButton Name=4on4Button
         Caption="4on4"
         FontScale=FNS_Small
         WinTop=0.400000
         WinLeft=0.350000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=3
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=4on4Button.InternalOnKeyEvent
     End Object
     B_4on4=GUIButton'ASeXtra.eXtraMenu.4on4Button'

     Begin Object Class=GUIButton Name=5on5Button
         Caption="5on5"
         FontScale=FNS_Small
         WinTop=0.450000
         WinLeft=0.350000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=4
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=5on5Button.InternalOnKeyEvent
     End Object
     B_5on5=GUIButton'ASeXtra.eXtraMenu.5on5Button'

     Begin Object Class=GUIButton Name=Train3on3Button
         Caption="Train(3on3)"
         FontScale=FNS_Small
         WinTop=0.350000
         WinLeft=0.500000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=5
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=Train3on3Button.InternalOnKeyEvent
     End Object
     B_Train3on3=GUIButton'ASeXtra.eXtraMenu.Train3on3Button'

     Begin Object Class=GUIButton Name=Train4on4Button
         Caption="Train(4on4)"
         FontScale=FNS_Small
         WinTop=0.400000
         WinLeft=0.500000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=6
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=Train4on4Button.InternalOnKeyEvent
     End Object
     B_Train4on4=GUIButton'ASeXtra.eXtraMenu.Train4on4Button'

     Begin Object Class=GUIButton Name=Train5on5Button
         Caption="Train(5on5)"
         FontScale=FNS_Small
         WinTop=0.450000
         WinLeft=0.500000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=7
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=Train5on5Button.InternalOnKeyEvent
     End Object
     B_Train5on5=GUIButton'ASeXtra.eXtraMenu.Train5on5Button'

     Begin Object Class=GUIButton Name=CustomButton
         Caption="Custom"
         FontScale=FNS_Small
         WinTop=0.500000
         WinLeft=0.350000
         WinWidth=0.300000
         WinHeight=0.050000
         TabOrder=8
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=CustomButton.InternalOnKeyEvent
     End Object
     B_Custom=GUIButton'ASeXtra.eXtraMenu.CustomButton'

     Begin Object Class=GUIButton Name=PauseButton
         Caption="Pause"
         FontScale=FNS_Small
         WinTop=0.575000
         WinLeft=0.350000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=9
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=PauseButton.InternalOnKeyEvent
     End Object
     B_Pause=GUIButton'ASeXtra.eXtraMenu.PauseButton'

     Begin Object Class=GUIButton Name=SwitchPlayersButton
         Caption="Switch Players"
         FontScale=FNS_Small
         WinTop=0.575000
         WinLeft=0.500000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=10
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=SwitchPlayersButton.InternalOnKeyEvent
     End Object
     B_SwitchPlayers=GUIButton'ASeXtra.eXtraMenu.SwitchPlayersButton'

     Begin Object Class=GUIButton Name=DNOButton
         Caption="Disable Next Obj"
         FontScale=FNS_Small
         WinTop=0.625000
         WinLeft=0.350000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=11
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=DNOButton.InternalOnKeyEvent
     End Object
     B_DNO=GUIButton'ASeXtra.eXtraMenu.DNOButton'

     Begin Object Class=GUIButton Name=RestartButton
         Caption="Restart Map"
         FontScale=FNS_Small
         WinTop=0.625000
         WinLeft=0.500000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=12
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=RestartButton.InternalOnKeyEvent
     End Object
     B_Restart=GUIButton'ASeXtra.eXtraMenu.RestartButton'

     Begin Object Class=GUIButton Name=SettingsButton
         Caption="AS eXtra Settings"
         FontScale=FNS_Small
         WinTop=0.675000
         WinLeft=0.350000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=13
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=SettingsButton.InternalOnKeyEvent
     End Object
     b_Settings=GUIButton'ASeXtra.eXtraMenu.SettingsButton'

     Begin Object Class=GUIButton Name=VoteButton
         Caption="Vote"
         FontScale=FNS_Small
         WinTop=0.675000
         WinLeft=0.500000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=14
         OnClick=eXtraMenu.InternalOnClick
         OnKeyEvent=VoteButton.InternalOnKeyEvent
     End Object
     B_Vote=GUIButton'ASeXtra.eXtraMenu.VoteButton'

     bAllowedAsLast=True
     WinTop=0.000000
     WinWidth=0.000000
     WinHeight=0.000000
}
