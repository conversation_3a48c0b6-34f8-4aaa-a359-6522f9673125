

defaultproperties
{
     Begin Object Class=FloatingImage Name=UsersBackGround
         Image=Texture'2K4Menus.NewControls.Display95'
         DropShadow=None
         ImageStyle=ISTY_Stretched
         ImageRenderStyle=MSTY_Normal
         WinTop=0.350000
         WinLeft=0.300000
         WinWidth=0.400000
         WinHeight=0.300000
         RenderWeight=0.030000
         bBoundToParent=False
         bScaleToParent=False
     End Object
     BG_Users=FloatingImage'ASeXtra.UsersMenu.UsersBackGround'

     Begin Object Class=GUILabel Name=AddLabel
         Caption="Add User:"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.280000
         WinLeft=0.351000
         WinWidth=0.300000
         WinHeight=0.250000
     End Object
     L_Add=GUILabel'ASeXtra.UsersMenu.AddLabel'

     Begin Object Class=GUILabel Name=RemoveLabel
         Caption="Remove User:"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.390000
         WinLeft=0.351000
         WinWidth=0.300000
         WinHeight=0.250000
     End Object
     L_Remove=GUILabel'ASeXtra.UsersMenu.RemoveLabel'

     Begin Object Class=GUIComboBox Name=AddCombo
         FontScale=FNS_Small
         WinTop=0.420000
         WinLeft=0.351000
         WinWidth=0.298000
         WinHeight=0.027500
         TabOrder=1
         OnKeyEvent=AddCombo.InternalOnKeyEvent
     End Object
     CB_Add=GUIComboBox'ASeXtra.UsersMenu.AddCombo'

     Begin Object Class=GUIComboBox Name=RemoveCombo
         FontScale=FNS_Small
         WinTop=0.530000
         WinLeft=0.351000
         WinWidth=0.298000
         WinHeight=0.027500
         TabOrder=3
         OnKeyEvent=RemoveCombo.InternalOnKeyEvent
     End Object
     CB_Remove=GUIComboBox'ASeXtra.UsersMenu.RemoveCombo'

     Begin Object Class=GUIButton Name=AddButton
         Caption="Add"
         FontScale=FNS_Small
         WinTop=0.447000
         WinLeft=0.350000
         WinWidth=0.300000
         WinHeight=0.048000
         TabOrder=2
         OnClick=UsersMenu.InternalOnClick
         OnKeyEvent=AddButton.InternalOnKeyEvent
     End Object
     b_Add=GUIButton'ASeXtra.UsersMenu.AddButton'

     Begin Object Class=GUIButton Name=RemoveButton
         Caption="Remove"
         FontScale=FNS_Small
         WinTop=0.557000
         WinLeft=0.350000
         WinWidth=0.300000
         WinHeight=0.048000
         TabOrder=4
         OnClick=UsersMenu.InternalOnClick
         OnKeyEvent=RemoveButton.InternalOnKeyEvent
     End Object
     b_Remove=GUIButton'ASeXtra.UsersMenu.RemoveButton'

     bAllowedAsLast=True
     WinTop=0.000000
     WinWidth=0.000000
     WinHeight=0.000000
}
