

defaultproperties
{
     Begin Object Class=FloatingImage Name=SettingsBackGround
         Image=Texture'2K4Menus.NewControls.Display95'
         DropShadow=None
         ImageStyle=ISTY_Stretched
         ImageRenderStyle=MSTY_Normal
         WinLeft=0.300000
         WinWidth=0.400000
         WinHeight=0.250000
         RenderWeight=0.020000
         bBoundToParent=False
         bScaleToParent=False
     End Object
     BG_Settings=FloatingImage'ASeXtra.SettingsMenu.SettingsBackGround'

     Begin Object Class=GUILabel Name=VoteTimeLabel
         Caption="ASeXtra Vote Time:"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.437500
         WinLeft=0.353000
         WinWidth=0.200000
         WinHeight=0.022500
     End Object
     L_VoteTime=GUILabel'ASeXtra.SettingsMenu.VoteTimeLabel'

     Begin Object Class=GUILabel Name=RestartTimeLabel
         Caption="Soft Restart Time:"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.467500
         WinLeft=0.353000
         WinWidth=0.200000
         WinHeight=0.022500
     End Object
     L_RestartTime=GUILabel'ASeXtra.SettingsMenu.RestartTimeLabel'

     Begin Object Class=GUILabel Name=UnPauseTimeLabel
         Caption="Unpause Time:"
         TextColor=(B=0,G=255,R=255)
         TextFont="UT2DefaultFont"
         WinTop=0.497500
         WinLeft=0.353000
         WinWidth=0.200000
         WinHeight=0.022500
     End Object
     L_UnPauseTime=GUILabel'ASeXtra.SettingsMenu.UnPauseTimeLabel'

     Begin Object Class=GUIEditBox Name=VoteTimeEdit
         TextStr="00"
         AllowedCharSet="1234567890"
         MaxWidth=2
         FontScale=FNS_Small
         WinTop=0.437500
         WinLeft=0.571000
         WinWidth=0.078000
         WinHeight=0.022500
         TabOrder=1
         OnActivate=VoteTimeEdit.InternalActivate
         OnDeActivate=VoteTimeEdit.InternalDeactivate
         OnKeyType=VoteTimeEdit.InternalOnKeyType
         OnKeyEvent=VoteTimeEdit.InternalOnKeyEvent
     End Object
     E_VoteTime=GUIEditBox'ASeXtra.SettingsMenu.VoteTimeEdit'

     Begin Object Class=GUIEditBox Name=RestartTimeEdit
         TextStr="00"
         AllowedCharSet="1234567890"
         MaxWidth=2
         FontScale=FNS_Small
         WinTop=0.467500
         WinLeft=0.571000
         WinWidth=0.078000
         WinHeight=0.022500
         TabOrder=2
         OnActivate=RestartTimeEdit.InternalActivate
         OnDeActivate=RestartTimeEdit.InternalDeactivate
         OnKeyType=RestartTimeEdit.InternalOnKeyType
         OnKeyEvent=RestartTimeEdit.InternalOnKeyEvent
     End Object
     E_RestartTime=GUIEditBox'ASeXtra.SettingsMenu.RestartTimeEdit'

     Begin Object Class=GUIEditBox Name=UnPauseTimeEdit
         TextStr="00"
         AllowedCharSet="1234567890"
         MaxWidth=2
         FontScale=FNS_Small
         WinTop=0.497500
         WinLeft=0.571000
         WinWidth=0.078000
         WinHeight=0.022500
         TabOrder=3
         OnActivate=UnPauseTimeEdit.InternalActivate
         OnDeActivate=UnPauseTimeEdit.InternalDeactivate
         OnKeyType=UnPauseTimeEdit.InternalOnKeyType
         OnKeyEvent=UnPauseTimeEdit.InternalOnKeyEvent
     End Object
     E_UnPauseTime=GUIEditBox'ASeXtra.SettingsMenu.UnPauseTimeEdit'

     Begin Object Class=GUIButton Name=ApplyButton
         Caption="Apply Settings"
         FontScale=FNS_Small
         WinTop=0.525000
         WinLeft=0.350000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=4
         OnClick=SettingsMenu.InternalOnClick
         OnKeyEvent=ApplyButton.InternalOnKeyEvent
     End Object
     b_Apply=GUIButton'ASeXtra.SettingsMenu.ApplyButton'

     Begin Object Class=GUIButton Name=UsersButton
         Caption="Users"
         FontScale=FNS_Small
         WinTop=0.525000
         WinLeft=0.500000
         WinWidth=0.150000
         WinHeight=0.050000
         TabOrder=5
         OnClick=SettingsMenu.InternalOnClick
         OnKeyEvent=UsersButton.InternalOnKeyEvent
     End Object
     B_Users=GUIButton'ASeXtra.SettingsMenu.UsersButton'

     bAllowedAsLast=True
     WinTop=0.000000
     WinWidth=0.000000
     WinHeight=0.000000
}
